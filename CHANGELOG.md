# Changelog


## v0.0.7

[compare changes](https://github.com/Sunny-117/eslint-plugin-react-boundary/compare/v0.0.6...v0.0.7)

### 📖 Documentation

- ✏️ update readme rules motivation ([0052e5f](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/0052e5f))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.6

[compare changes](https://github.com/Sunny-117/eslint-plugin-react-boundary/compare/v0.0.5...v0.0.6)

### 🚀 Enhancements

- 🎸 require-with-boundary ([87e6686](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/87e6686))

### 💅 Refactors

- 💡 test cases ([5a0e407](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/5a0e407))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.5

[compare changes](https://github.com/Sunny-117/eslint-plugin-react-boundary/compare/v0.0.4...v0.0.5)

### 🚀 Enhancements

- 🎸 支持boundaryComponent数组 ([7970c41](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/7970c41))

### 📖 Documentation

- ✏️ fix test ([241b59b](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/241b59b))
- ✏️ update readme ([95dbaf9](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/95dbaf9))

### ✅ Tests

- 💍 remove test demos ([78821ad](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/78821ad))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.4

[compare changes](https://github.com/Sunny-117/eslint-plugin-react-boundary/compare/v0.0.3...v0.0.4)

### 🏡 Chore

- 🤖 package.json ([2493d9a](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/2493d9a))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.3


### 🚀 Enhancements

- Init ([dfb07bd](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/dfb07bd))
- Release ([1f2559e](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/1f2559e))
- Playground ([58fc2a6](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/58fc2a6))
- Flat config ([e49e467](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/e49e467))
- FunctionDeclaration and VariableDeclaration ([e0148aa](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/e0148aa))
- Playground cases ([3e45a79](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/3e45a79))

### 💅 Refactors

- 💡 test cases ([215573b](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/215573b))

### 🏡 Chore

- Fix link ([320f51b](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/320f51b))
- Gitignore ([007d579](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/007d579))
- Release ([4d438e8](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/4d438e8))
- Remove examples ([94896d2](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/94896d2))

### ✅ Tests

- Add withBoundary ([d15a805](https://github.com/Sunny-117/eslint-plugin-react-boundary/commit/d15a805))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

