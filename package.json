{"name": "eslint-plugin-react-boundary", "version": "0.0.4", "description": "ESLint plugin to ensure React components are wrapped with Boundary", "main": "index.js", "scripts": {"test": "npm run test-plugin && npm run test-cases && npm run test-separate", "test-plugin": "node test/test-plugin.js", "test-cases": "node test/test-cases.js", "test-separate": "node test/test-separate-export.js", "commit": "git-cz", "release": "changelogen --release && npm publish"}, "keywords": ["eslint", "eslintplugin", "react", "boundary", "error-boundary", "suspense"], "author": "Your Name", "license": "MIT", "peerDependencies": {"eslint": ">=7.0.0"}, "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "https://github.com/Sunny-117/eslint-plugin-react-boundary.git"}, "bugs": {"url": "https://github.com/Sunny-117/eslint-plugin-react-boundary/issues"}, "homepage": "https://github.com/Sunny-117/eslint-plugin-react-boundary#readme", "devDependencies": {"changelogen": "^0.6.2", "git-cz": "^4.9.0"}}