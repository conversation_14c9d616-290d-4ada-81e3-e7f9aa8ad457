// 这个文件演示两个规则的区别

import { Boundary, withBoundary } from 'react-suspense-boundary';

// ========== require-boundary 规则示例 ==========

// ✅ require-boundary: 正确 - 组件被 <Boundary> 包装
function ComponentWithBoundary() {
  return (
    <Boundary>
      <div>This component is wrapped with Boundary</div>
    </Boundary>
  );
}

// ❌ require-boundary: 错误 - 组件没有被 <Boundary> 包装
function ComponentWithoutBoundary() {
  return <div>This component is NOT wrapped with Boundary</div>;
}

// ========== require-with-boundary 规则示例 ==========

// ✅ require-with-boundary: 正确 - 使用 withBoundary HOC 包装后导出
function MyComponent() {
  return <div>Component content</div>;
}

// ❌ require-with-boundary: 错误 - 直接导出组件
function DirectExportComponent() {
  return <div>Direct export component</div>;
}

// ========== 导出示例 ==========

// require-boundary 规则关注的是组件内部是否用 <Boundary> 包装
export { ComponentWithBoundary, ComponentWithoutBoundary };

// require-with-boundary 规则关注的是导出时是否用 withBoundary() 包装
export default withBoundary(MyComponent); // ✅ 正确
// export default MyComponent; // ❌ 错误

export { DirectExportComponent }; // ❌ require-with-boundary 会报错

// 正确的方式应该是：
const WrappedComponent = withBoundary(DirectExportComponent);
export { WrappedComponent };
